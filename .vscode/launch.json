{"version": "0.2.0", "configurations": [{"name": "Depurar programa C++", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/programa", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Habilitar formato pretty para gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}