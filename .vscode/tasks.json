{"version": "2.0.0", "tasks": [{"label": "Compilar C++ (múltiples archivos)", "type": "shell", "command": "g++", "args": ["*.cpp", "-g", "-o", "programa"], "group": "build", "problemMatcher": ["$gcc"]}, {"type": "cppbuild", "label": "C/C++: g++ compilar archivo activo", "command": "/usr/bin/g++", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Tarea generada por el depurador."}]}